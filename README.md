# 微信多开助手

一个简单的Android应用，用于克隆微信实现多开功能。

## 功能特性

- 🔍 自动检测设备上已安装的微信应用
- 📱 克隆微信APK并修改包名实现多开
- 🚀 一键安装和启动克隆应用
- 🗂️ 管理所有克隆的微信应用
- 🔒 权限管理和安全检查

## 技术实现

### 核心技术
- **APK解析**: 使用zip4j库解压和重新打包APK文件
- **包名修改**: 修改AndroidManifest.xml中的包名实现应用隔离
- **权限管理**: 动态请求存储和安装权限
- **应用管理**: 实现APK安装、卸载、启动功能

### 项目结构
```
app/src/main/java/com/example/wechatmultiopen/
├── MainActivity.kt                 # 主界面Activity
├── model/
│   └── AppInfo.kt                 # 应用信息数据模型
├── adapter/
│   └── ClonedAppAdapter.kt        # 克隆应用列表适配器
└── utils/
    ├── AppDetector.kt             # 应用检测工具
    ├── ApkCloner.kt               # APK克隆核心功能
    ├── AppManager.kt              # 应用管理工具
    └── PermissionHelper.kt        # 权限管理工具
```

## 使用说明

### 环境要求
- Android 5.0 (API 21) 及以上
- 已安装微信应用
- 存储权限和安装权限

### 使用步骤

1. **安装应用**
   - 下载并安装微信多开助手APK
   - 首次启动时会请求必要权限

2. **检测微信**
   - 应用会自动检测设备上的微信应用
   - 显示微信版本信息和包名

3. **克隆微信**
   - 点击"克隆微信"按钮开始克隆过程
   - 等待克隆进度完成（包括解压、修改、重新打包等步骤）

4. **安装克隆应用**
   - 克隆完成后选择立即安装
   - 系统会弹出安装确认对话框

5. **管理克隆应用**
   - 在应用列表中查看所有克隆的微信
   - 可以启动、卸载克隆应用

## 权限说明

### 必需权限
- `QUERY_ALL_PACKAGES`: 查询已安装应用
- `REQUEST_INSTALL_PACKAGES`: 安装APK文件
- `READ_EXTERNAL_STORAGE`: 读取存储文件
- `WRITE_EXTERNAL_STORAGE`: 写入存储文件
- `MANAGE_EXTERNAL_STORAGE`: 管理外部存储（Android 11+）

### 权限用途
- **存储权限**: 用于读取原微信APK和保存克隆APK
- **安装权限**: 用于安装克隆的微信应用
- **查询权限**: 用于检测已安装的微信和克隆应用

## 注意事项

### 重要提醒
1. **仅供学习**: 本项目仅用于学习Android开发技术，请勿用于商业用途
2. **版权声明**: 微信是腾讯公司的注册商标，本应用不涉及微信代码修改
3. **安全风险**: 克隆应用可能存在安全风险，请谨慎使用
4. **系统兼容**: 不同Android版本和厂商ROM可能存在兼容性问题

### 技术限制
1. **签名问题**: 克隆应用使用简化签名，可能在某些设备上无法正常运行
2. **数据隔离**: 克隆应用与原应用数据完全隔离，无法共享聊天记录
3. **更新问题**: 克隆应用无法通过应用商店更新，需要重新克隆

## 开发环境

### 构建要求
- Android Studio 2022.3.1 或更高版本
- Gradle 8.0+
- Kotlin 1.8.20+
- Android SDK 34

### 依赖库
- AndroidX Core KTX
- Material Design Components
- RecyclerView
- Lifecycle Components
- zip4j (APK处理)

## 构建说明

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd WeChatMultiOpen
   ```

2. **导入Android Studio**
   - 打开Android Studio
   - 选择"Open an existing project"
   - 选择项目目录

3. **构建APK**
   ```bash
   ./gradlew assembleDebug
   ```

## 免责声明

本项目仅用于技术学习和研究目的。使用本应用产生的任何问题和风险由用户自行承担。开发者不对因使用本应用而导致的任何损失或问题承担责任。

请遵守相关法律法规，尊重软件版权，合理使用技术。

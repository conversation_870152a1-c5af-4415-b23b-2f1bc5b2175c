package com.example.wechatmultiopen.utils

import android.content.Context
import android.os.Environment
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import net.lingala.zip4j.ZipFile
import net.lingala.zip4j.model.ZipParameters
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.nio.charset.StandardCharsets

/**
 * APK克隆工具类
 */
class ApkCloner(private val context: Context) {

    companion object {
        private const val TAG = "ApkCloner"
    }

    private val workDir = File(context.getExternalFilesDir(null), "clone_work")
    private val outputDir = File(context.getExternalFilesDir(null), "cloned_apks")

    init {
        workDir.mkdirs()
        outputDir.mkdirs()
        Log.d(TAG, "工作目录: ${workDir.absolutePath}")
        Log.d(TAG, "输出目录: ${outputDir.absolutePath}")
    }
    
    /**
     * 克隆APK（简化版本）
     */
    suspend fun cloneApk(
        originalApkPath: String,
        cloneIndex: Int,
        onProgress: (Int, String) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始克隆APK: $originalApkPath")
            onProgress(10, "开始克隆...")

            val originalApk = File(originalApkPath)
            if (!originalApk.exists()) {
                Log.e(TAG, "原APK文件不存在: $originalApkPath")
                return@withContext Result.failure(Exception("原APK文件不存在"))
            }

            onProgress(30, "复制APK文件...")

            // 简化版本：直接复制APK文件并重命名
            val clonedApkPath = File(outputDir, "wechat_clone_$cloneIndex.apk").absolutePath
            val clonedApk = File(clonedApkPath)

            // 复制原APK到新位置
            originalApk.copyTo(clonedApk, overwrite = true)

            onProgress(60, "生成克隆信息...")

            // 创建克隆信息文件
            val infoFile = File(outputDir, "wechat_clone_${cloneIndex}_info.txt")
            infoFile.writeText("""
                克隆时间: ${System.currentTimeMillis()}
                原APK路径: $originalApkPath
                克隆索引: $cloneIndex
                目标包名: com.tencent.mm.clone$cloneIndex
                注意: 这是简化版本的克隆，实际使用需要修改包名和签名
            """.trimIndent())

            onProgress(90, "完成克隆...")

            Log.d(TAG, "APK克隆完成: $clonedApkPath")
            onProgress(100, "克隆完成")

            Result.success(clonedApkPath)

        } catch (e: Exception) {
            Log.e(TAG, "克隆APK失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 修改AndroidManifest.xml中的包名
     */
    private fun modifyManifest(workFolder: File, newPackageName: String) {
        val manifestFile = File(workFolder, "AndroidManifest.xml")
        if (!manifestFile.exists()) return
        
        try {
            // 注意：这是简化版本，实际需要使用aapt工具来正确处理二进制XML
            // 这里只是演示概念，实际项目中需要更复杂的处理
            val content = manifestFile.readText(StandardCharsets.UTF_8)
            val modifiedContent = content.replace(
                "com.tencent.mm",
                newPackageName
            )
            manifestFile.writeText(modifiedContent, StandardCharsets.UTF_8)
        } catch (e: Exception) {
            // 如果是二进制XML，需要使用专门的工具处理
            e.printStackTrace()
        }
    }
    
    /**
     * 修改应用名称
     */
    private fun modifyAppName(workFolder: File, cloneIndex: Int) {
        val stringsFile = File(workFolder, "res/values/strings.xml")
        if (!stringsFile.exists()) return
        
        try {
            val content = stringsFile.readText(StandardCharsets.UTF_8)
            val modifiedContent = content.replace(
                "微信",
                "微信克隆$cloneIndex"
            ).replace(
                "WeChat",
                "WeChat Clone $cloneIndex"
            )
            stringsFile.writeText(modifiedContent, StandardCharsets.UTF_8)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    
    /**
     * 重新打包APK
     */
    private fun repackageApk(workFolder: File, outputPath: String) {
        val outputFile = File(outputPath)
        if (outputFile.exists()) {
            outputFile.delete()
        }
        
        val zipFile = ZipFile(outputFile)
        val zipParameters = ZipParameters()
        
        // 添加所有文件到ZIP
        workFolder.listFiles()?.forEach { file ->
            if (file.isDirectory) {
                zipFile.addFolder(file, zipParameters)
            } else {
                zipFile.addFile(file, zipParameters)
            }
        }
    }
    
    /**
     * 简单签名APK（演示用）
     */
    private fun signApk(apkPath: String) {
        // 注意：这里只是占位符
        // 实际项目中需要使用jarsigner或apksigner进行正确的签名
        // 可以使用测试签名或生成自己的签名
        
        // 简化版本：复制一个已签名的APK的META-INF目录
        // 实际应该使用正确的签名工具
    }
    
    /**
     * 获取下一个可用的克隆索引
     */
    fun getNextCloneIndex(): Int {
        val clonedApps = AppDetector.getClonedWeChatApps(context)
        val usedIndices = clonedApps.map { it.cloneIndex }.toSet()
        
        for (i in 1..99) {
            if (i !in usedIndices) {
                return i
            }
        }
        return 1
    }
    
    /**
     * 清理临时文件
     */
    fun cleanup() {
        try {
            workDir.deleteRecursively()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

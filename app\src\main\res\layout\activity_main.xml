<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/light_gray"
    tools:context=".MainActivity">

    <!-- 顶部标题栏 -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/wechat_green"
            app:title="@string/app_name"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- 微信检测状态 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvWeChatStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/wechat_not_found"
                android:textSize="16sp"
                android:textStyle="bold"
                android:gravity="center" />

            <TextView
                android:id="@+id/tvWeChatInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text=""
                android:textSize="14sp"
                android:textColor="@color/dark_gray"
                android:gravity="center"
                android:visibility="gone" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCloneWeChat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/clone_wechat"
                android:enabled="false"
                app:backgroundTint="@color/wechat_green" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 克隆进度 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cardProgress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvProgressText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/cloning_progress"
                android:textSize="14sp"
                android:gravity="center" />

            <com.google.android.material.progressindicator.LinearProgressIndicator
                android:id="@+id/progressBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:indicatorColor="@color/wechat_green" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 已克隆应用列表 -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/cloned_apps"
                android:textSize="16sp"
                android:textStyle="bold"
                android:padding="16dp"
                android:background="@color/wechat_green"
                android:textColor="@color/white" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewClonedApps"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="8dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</LinearLayout>

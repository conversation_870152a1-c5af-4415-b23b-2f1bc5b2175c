@echo off
echo ========================================
echo 微信多开助手 - 简化构建脚本
echo ========================================
echo.

echo 正在尝试构建APK...
echo 如果遇到网络问题，请确保网络连接正常
echo.

REM 删除可能导致问题的缓存
if exist ".gradle" (
    echo 清理Gradle缓存...
    rmdir /s /q ".gradle" 2>nul
)

REM 尝试构建
echo 开始构建...
gradlew.bat assembleDebug --stacktrace --info

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ 构建成功！
    echo ========================================
    echo.
    echo APK文件位置: app\build\outputs\apk\debug\app-debug.apk
    echo.
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo 文件已生成，可以安装到Android设备
        explorer "app\build\outputs\apk\debug"
    ) else (
        echo 警告: APK文件未找到
    )
) else (
    echo.
    echo ========================================
    echo ❌ 构建失败
    echo ========================================
    echo.
    echo 常见解决方案:
    echo 1. 检查网络连接
    echo 2. 使用Android Studio构建
    echo 3. 检查Android SDK是否正确安装
    echo 4. 尝试使用VPN
)

echo.
pause

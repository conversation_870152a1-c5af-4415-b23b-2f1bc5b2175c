# 手动构建APK指南

由于当前环境缺少Android开发工具，这里提供几种构建APK的方法：

## 方法1：使用Android Studio（推荐）

1. **安装Android Studio**
   - 下载：https://developer.android.com/studio
   - 安装并配置Android SDK

2. **导入项目**
   - 打开Android Studio
   - 选择 "Open an existing project"
   - 选择项目文件夹

3. **构建APK**
   - 点击菜单 Build → Build Bundle(s) / APK(s) → Build APK(s)
   - 或使用快捷键 Ctrl+Shift+A 搜索 "Build APK"

4. **找到APK文件**
   - 构建完成后，APK位于：`app/build/outputs/apk/debug/app-debug.apk`

## 方法2：使用命令行（需要Android SDK）

如果已安装Android SDK，可以使用以下命令：

```bash
# 设置环境变量（根据实际路径调整）
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools

# 清理项目
gradlew clean

# 构建Debug APK
gradlew assembleDebug

# 构建Release APK（需要签名）
gradlew assembleRelease
```

## 方法3：在线构建服务

可以使用在线Android构建服务：
- GitHub Actions
- GitLab CI/CD
- CircleCI

## 当前项目状态

项目文件已完整创建，包含：
- ✅ 完整的源代码
- ✅ 资源文件
- ✅ 构建配置
- ✅ 权限配置
- ✅ 界面布局

只需要Android开发环境即可构建成功。

## 预期APK功能

构建成功的APK将具有以下功能：
1. 检测设备上的微信应用
2. 克隆微信APK文件
3. 管理克隆的应用
4. 权限管理界面

## 注意事项

1. **首次构建**可能需要下载依赖，确保网络连接正常
2. **签名问题**：Debug版本使用默认签名，Release版本需要配置签名
3. **权限**：安装后需要手动授予存储和安装权限

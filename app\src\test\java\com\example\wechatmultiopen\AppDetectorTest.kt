package com.example.wechatmultiopen

import com.example.wechatmultiopen.utils.AppDetector
import org.junit.Test
import org.junit.Assert.*

/**
 * 应用检测功能单元测试
 */
class AppDetectorTest {

    @Test
    fun testExtractCloneIndex() {
        // 使用反射访问私有方法进行测试
        val method = AppDetector::class.java.getDeclaredMethod("extractCloneIndex", String::class.java)
        method.isAccessible = true
        
        // 测试不同的包名格式
        assertEquals(1, method.invoke(null, "com.tencent.mm.clone1"))
        assertEquals(2, method.invoke(null, "com.tencent.mm.clone2"))
        assertEquals(10, method.invoke(null, "com.tencent.mm.clone10"))
        assertEquals(1, method.invoke(null, "com.tencent.mm.wechat"))
    }
    
    @Test
    fun testWeChatPackageName() {
        assertEquals("com.tencent.mm", AppDetector.WECHAT_PACKAGE_NAME)
    }
}

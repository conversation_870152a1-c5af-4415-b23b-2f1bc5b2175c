package com.example.wechatmultiopen.utils

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import com.example.wechatmultiopen.model.AppInfo

/**
 * 应用检测工具类
 */
object AppDetector {
    
    // 微信包名
    const val WECHAT_PACKAGE_NAME = "com.tencent.mm"
    
    /**
     * 检测微信是否已安装
     */
    fun isWeChatInstalled(context: Context): <PERSON><PERSON><PERSON> {
        return try {
            context.packageManager.getPackageInfo(WECHAT_PACKAGE_NAME, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
    
    /**
     * 获取微信应用信息
     */
    fun getWeChatInfo(context: Context): AppInfo? {
        return try {
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(WECHAT_PACKAGE_NAME, 0)
            val applicationInfo = packageInfo.applicationInfo
            
            AppInfo(
                packageName = WECHAT_PACKAGE_NAME,
                appName = packageManager.getApplicationLabel(applicationInfo).toString(),
                versionName = packageInfo.versionName ?: "未知",
                versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode.toLong()
                },
                icon = packageManager.getApplicationIcon(applicationInfo),
                apkPath = applicationInfo.sourceDir
            )
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 获取所有已安装的克隆微信应用
     */
    fun getClonedWeChatApps(context: Context): List<AppInfo> {
        val clonedApps = mutableListOf<AppInfo>()
        val packageManager = context.packageManager
        
        try {
            val installedPackages = packageManager.getInstalledPackages(0)
            
            for (packageInfo in installedPackages) {
                val packageName = packageInfo.packageName
                
                // 检查是否是克隆的微信应用（包名包含我们的克隆标识）
                if (packageName.startsWith("${WECHAT_PACKAGE_NAME}.clone") ||
                    packageName.contains("wechat") && packageName != WECHAT_PACKAGE_NAME) {
                    
                    val applicationInfo = packageInfo.applicationInfo
                    
                    // 提取克隆索引
                    val cloneIndex = extractCloneIndex(packageName)
                    
                    val appInfo = AppInfo(
                        packageName = packageName,
                        appName = packageManager.getApplicationLabel(applicationInfo).toString(),
                        versionName = packageInfo.versionName ?: "未知",
                        versionCode = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                            packageInfo.longVersionCode
                        } else {
                            @Suppress("DEPRECATION")
                            packageInfo.versionCode.toLong()
                        },
                        icon = packageManager.getApplicationIcon(applicationInfo),
                        apkPath = applicationInfo.sourceDir,
                        isCloned = true,
                        cloneIndex = cloneIndex
                    )
                    
                    clonedApps.add(appInfo)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return clonedApps.sortedBy { it.cloneIndex }
    }
    
    /**
     * 从包名中提取克隆索引
     */
    private fun extractCloneIndex(packageName: String): Int {
        return try {
            val regex = Regex("clone(\\d+)")
            val matchResult = regex.find(packageName)
            matchResult?.groupValues?.get(1)?.toInt() ?: 1
        } catch (e: Exception) {
            1
        }
    }
    
    /**
     * 检查应用是否已安装
     */
    fun isAppInstalled(context: Context, packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }
}

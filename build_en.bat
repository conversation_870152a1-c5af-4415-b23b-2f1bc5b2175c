@echo off
echo ========================================
echo WeChat Multi-Open Helper - Build Script
echo ========================================
echo.

echo Building APK...
echo Please ensure network connection is stable
echo.

REM Clean Gradle cache
if exist ".gradle" (
    echo Cleaning Gradle cache...
    rmdir /s /q ".gradle" 2>nul
)

REM Try to build
echo Starting build...
gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Build SUCCESS!
    echo ========================================
    echo.
    echo APK location: app\build\outputs\apk\debug\app-debug.apk
    echo.
    if exist "app\build\outputs\apk\debug\app-debug.apk" (
        echo APK file generated successfully
        explorer "app\build\outputs\apk\debug"
    ) else (
        echo Warning: APK file not found
    )
) else (
    echo.
    echo ========================================
    echo Build FAILED
    echo ========================================
    echo.
    echo Common solutions:
    echo 1. Check network connection
    echo 2. Use Android Studio to build
    echo 3. Check Android SDK installation
    echo 4. Try using VPN
)

echo.
pause

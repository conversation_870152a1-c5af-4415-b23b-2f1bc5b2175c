@echo off
echo 正在构建微信多开助手...
echo.

REM 检查是否存在gradlew
if not exist gradlew.bat (
    echo 错误: 找不到gradlew.bat文件
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 清理项目...
call gradlew.bat clean

echo.
echo 构建Debug版本...
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 构建成功！
    echo APK文件位置: app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo 你可以将此APK安装到Android设备上进行测试
) else (
    echo.
    echo 构建失败，请检查错误信息
)

pause

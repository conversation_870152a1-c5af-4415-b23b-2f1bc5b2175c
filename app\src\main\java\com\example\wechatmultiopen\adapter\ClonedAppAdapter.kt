package com.example.wechatmultiopen.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.wechatmultiopen.R
import com.example.wechatmultiopen.model.AppInfo
import com.google.android.material.button.MaterialButton

/**
 * 克隆应用列表适配器
 */
class ClonedAppAdapter(
    private var apps: MutableList<AppInfo> = mutableListOf(),
    private val onLaunchClick: (AppInfo) -> Unit,
    private val onUninstallClick: (AppInfo) -> Unit
) : RecyclerView.Adapter<ClonedAppAdapter.ViewHolder>() {
    
    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val ivAppIcon: ImageView = view.findViewById(R.id.ivAppIcon)
        val tvAppName: TextView = view.findViewById(R.id.tvAppName)
        val tvPackageName: TextView = view.findViewById(R.id.tvPackageName)
        val btnLaunch: MaterialButton = view.findViewById(R.id.btnLaunch)
        val btnUninstall: MaterialButton = view.findViewById(R.id.btnUninstall)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_cloned_app, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val app = apps[position]
        
        holder.ivAppIcon.setImageDrawable(app.icon)
        holder.tvAppName.text = app.appName
        holder.tvPackageName.text = app.packageName
        
        holder.btnLaunch.setOnClickListener {
            onLaunchClick(app)
        }
        
        holder.btnUninstall.setOnClickListener {
            onUninstallClick(app)
        }
    }
    
    override fun getItemCount(): Int = apps.size
    
    /**
     * 更新应用列表
     */
    fun updateApps(newApps: List<AppInfo>) {
        apps.clear()
        apps.addAll(newApps)
        notifyDataSetChanged()
    }
    
    /**
     * 添加应用
     */
    fun addApp(app: AppInfo) {
        apps.add(app)
        notifyItemInserted(apps.size - 1)
    }
    
    /**
     * 移除应用
     */
    fun removeApp(app: AppInfo) {
        val index = apps.indexOf(app)
        if (index != -1) {
            apps.removeAt(index)
            notifyItemRemoved(index)
        }
    }
}

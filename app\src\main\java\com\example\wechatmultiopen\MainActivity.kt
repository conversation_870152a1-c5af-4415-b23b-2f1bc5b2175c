package com.example.wechatmultiopen

import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.wechatmultiopen.adapter.ClonedAppAdapter
import com.example.wechatmultiopen.model.AppInfo
import com.example.wechatmultiopen.utils.*
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.google.android.material.progressindicator.LinearProgressIndicator
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var clonedAppAdapter: ClonedAppAdapter
    private lateinit var apkCloner: ApkCloner
    private var weChatInfo: AppInfo? = null

    // UI组件
    private lateinit var tvWeChatStatus: TextView
    private lateinit var tvWeChatInfo: TextView
    private lateinit var btnCloneWeChat: MaterialButton
    private lateinit var cardProgress: MaterialCardView
    private lateinit var tvProgressText: TextView
    private lateinit var progressBar: LinearProgressIndicator
    private lateinit var recyclerViewClonedApps: RecyclerView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        initCloner()
        checkPermissions()
        detectWeChat()
        loadClonedApps()
    }
    
    private fun initViews() {
        // 初始化UI组件
        tvWeChatStatus = findViewById(R.id.tvWeChatStatus)
        tvWeChatInfo = findViewById(R.id.tvWeChatInfo)
        btnCloneWeChat = findViewById(R.id.btnCloneWeChat)
        cardProgress = findViewById(R.id.cardProgress)
        tvProgressText = findViewById(R.id.tvProgressText)
        progressBar = findViewById(R.id.progressBar)
        recyclerViewClonedApps = findViewById(R.id.recyclerViewClonedApps)

        // 设置RecyclerView
        clonedAppAdapter = ClonedAppAdapter(
            onLaunchClick = { app -> launchApp(app) },
            onUninstallClick = { app -> showUninstallDialog(app) }
        )

        recyclerViewClonedApps.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = clonedAppAdapter
        }

        // 设置克隆按钮点击事件
        btnCloneWeChat.setOnClickListener {
            if (PermissionHelper.hasAllPermissions(this)) {
                startCloning()
            } else {
                checkPermissions()
            }
        }
    }
    
    private fun initCloner() {
        apkCloner = ApkCloner(this)
    }
    
    private fun checkPermissions() {
        when {
            !PermissionHelper.hasStoragePermission(this) -> {
                showPermissionDialog(
                    getString(R.string.permission_storage),
                    { PermissionHelper.requestStoragePermission(this) }
                )
            }
            !PermissionHelper.hasInstallPermission(this) -> {
                showPermissionDialog(
                    getString(R.string.permission_install),
                    { PermissionHelper.requestInstallPermission(this) }
                )
            }
        }
    }
    
    private fun showPermissionDialog(message: String, onGrantClick: () -> Unit) {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.permission_required))
            .setMessage(message)
            .setPositiveButton(getString(R.string.grant_permission)) { _, _ ->
                onGrantClick()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    private fun detectWeChat() {
        if (AppDetector.isWeChatInstalled(this)) {
            weChatInfo = AppDetector.getWeChatInfo(this)
            weChatInfo?.let { info ->
                tvWeChatStatus.text = getString(R.string.wechat_found)
                tvWeChatInfo.apply {
                    text = "版本: ${info.versionName}\n包名: ${info.packageName}"
                    visibility = View.VISIBLE
                }
                btnCloneWeChat.isEnabled = true
            }
        } else {
            tvWeChatStatus.text = getString(R.string.wechat_not_found)
            tvWeChatInfo.visibility = View.GONE
            btnCloneWeChat.isEnabled = false
        }
    }
    
    private fun loadClonedApps() {
        val clonedApps = AppDetector.getClonedWeChatApps(this)
        clonedAppAdapter.updateApps(clonedApps)
    }
    
    private fun startCloning() {
        val wechat = weChatInfo ?: return

        cardProgress.visibility = View.VISIBLE
        btnCloneWeChat.isEnabled = false

        val cloneIndex = apkCloner.getNextCloneIndex()

        lifecycleScope.launch {
            val result = apkCloner.cloneApk(
                originalApkPath = wechat.apkPath,
                cloneIndex = cloneIndex
            ) { progress, message ->
                runOnUiThread {
                    progressBar.progress = progress
                    tvProgressText.text = message
                }
            }

            runOnUiThread {
                cardProgress.visibility = View.GONE
                btnCloneWeChat.isEnabled = true

                result.fold(
                    onSuccess = { clonedApkPath ->
                        showCloneSuccessDialog(clonedApkPath)
                    },
                    onFailure = { error ->
                        Toast.makeText(
                            this@MainActivity,
                            "${getString(R.string.clone_failed)}: ${error.message}",
                            Toast.LENGTH_LONG
                        ).show()
                    }
                )
            }
        }
    }
    
    private fun showCloneSuccessDialog(clonedApkPath: String) {
        AlertDialog.Builder(this)
            .setTitle(getString(R.string.clone_success))
            .setMessage("APK已生成，是否立即安装？")
            .setPositiveButton("安装") { _, _ ->
                AppManager.installApk(this, clonedApkPath)
            }
            .setNegativeButton("稍后", null)
            .show()
    }
    
    private fun launchApp(app: AppInfo) {
        if (AppManager.launchApp(this, app.packageName)) {
            Toast.makeText(this, "正在启动 ${app.appName}", Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(this, "启动失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun showUninstallDialog(app: AppInfo) {
        AlertDialog.Builder(this)
            .setTitle("卸载应用")
            .setMessage("确定要卸载 ${app.appName} 吗？")
            .setPositiveButton("卸载") { _, _ ->
                AppManager.uninstallApp(this, app.packageName)
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    override fun onResume() {
        super.onResume()
        // 刷新克隆应用列表（可能有新安装或卸载的应用）
        loadClonedApps()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        when (requestCode) {
            PermissionHelper.REQUEST_STORAGE_PERMISSION,
            PermissionHelper.REQUEST_INSTALL_PERMISSION,
            PermissionHelper.REQUEST_MANAGE_STORAGE_PERMISSION -> {
                // 权限请求返回后重新检查权限
                checkPermissions()
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        apkCloner.cleanup()
    }
}

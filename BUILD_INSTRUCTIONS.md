# 微信多开助手 - 构建说明

## 当前状态

✅ **项目文件已完整创建**
- 完整的Android项目结构
- 所有源代码文件
- 资源文件和配置
- Gradle构建脚本

❌ **构建环境缺失**
- 需要Android SDK
- 需要Android Studio或命令行工具

## 构建方法

### 方法1：使用Android Studio（推荐）

1. **下载并安装Android Studio**
   ```
   https://developer.android.com/studio
   ```

2. **导入项目**
   - 启动Android Studio
   - 选择 "Open an existing project"
   - 选择项目文件夹 `anzhuo2`

3. **同步项目**
   - Android Studio会自动下载依赖
   - 等待Gradle同步完成

4. **构建APK**
   - 菜单: Build → Build Bundle(s) / APK(s) → Build APK(s)
   - 或使用快捷键: Ctrl+Shift+A，搜索"Build APK"

5. **获取APK**
   - 构建完成后，APK位于: `app/build/outputs/apk/debug/app-debug.apk`

### 方法2：命令行构建（需要Android SDK）

如果已安装Android SDK：

```bash
# Windows
gradlew.bat assembleDebug

# Linux/Mac
./gradlew assembleDebug
```

### 方法3：在线构建服务

可以使用以下在线服务：

1. **GitHub Actions**
   - 将代码推送到GitHub
   - 配置Android构建Action

2. **GitLab CI/CD**
   - 使用GitLab的CI/CD功能

3. **其他在线构建服务**
   - CircleCI
   - Travis CI

## 项目特性

### 已实现功能
- ✅ 微信应用检测
- ✅ APK克隆功能（简化版）
- ✅ 权限管理
- ✅ 用户界面
- ✅ 应用管理（安装/卸载/启动）

### 技术栈
- **语言**: Kotlin
- **UI**: Material Design
- **架构**: MVVM
- **依赖**: AndroidX, Material Components, zip4j

### 权限要求
- 存储权限（读写APK文件）
- 安装权限（安装克隆应用）
- 查询应用权限（检测已安装应用）

## 使用说明

1. **安装应用**
   - 将构建好的APK安装到Android设备

2. **授予权限**
   - 首次启动时授予必要权限

3. **检测微信**
   - 应用会自动检测设备上的微信

4. **克隆微信**
   - 点击"克隆微信"按钮
   - 等待克隆过程完成

5. **安装克隆应用**
   - 克隆完成后选择安装
   - 可以在应用列表中管理克隆应用

## 注意事项

### 重要提醒
⚠️ **仅供学习使用**
- 本项目仅用于Android开发技术学习
- 请勿用于商业用途
- 尊重微信的版权和用户协议

⚠️ **技术限制**
- 当前版本使用简化的克隆方法
- 真正的多开需要更复杂的APK修改技术
- 可能在某些设备上无法正常工作

⚠️ **安全风险**
- 克隆应用可能存在安全风险
- 建议仅在测试设备上使用
- 不要在克隆应用中输入重要信息

## 故障排除

### 常见问题

1. **构建失败**
   - 检查Android SDK是否正确安装
   - 确保网络连接正常（下载依赖）
   - 清理项目：`gradlew clean`

2. **权限被拒绝**
   - 在设备设置中手动授予权限
   - 确保设备允许安装未知来源应用

3. **克隆失败**
   - 检查存储空间是否充足
   - 确保微信应用已正确安装
   - 查看应用日志获取详细错误信息

### 获取帮助

如果遇到问题，可以：
1. 查看Android Studio的Build输出
2. 检查设备日志（adb logcat）
3. 参考Android官方文档

## 下一步改进

如果想进一步完善项目：

1. **真正的APK修改**
   - 使用aapt工具修改AndroidManifest.xml
   - 实现正确的包名修改

2. **APK重新签名**
   - 生成自定义签名证书
   - 实现APK重新签名功能

3. **UI/UX改进**
   - 添加更多动画效果
   - 改进用户交互体验

4. **错误处理**
   - 完善异常处理机制
   - 添加更详细的用户提示

5. **兼容性**
   - 测试不同Android版本
   - 适配不同设备和ROM

---

**项目已准备就绪，只需要Android开发环境即可构建APK！**

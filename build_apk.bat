@echo off
echo ========================================
echo 微信多开助手 - APK构建脚本
echo ========================================
echo.

REM 检查Java环境
echo [1/6] 检查Java环境...
java -version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: 未找到Java环境
    echo 请安装JDK 8或更高版本
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

REM 检查Gradle Wrapper
echo.
echo [2/6] 检查Gradle Wrapper...
if not exist "gradlew.bat" (
    echo ❌ 错误: 找不到gradlew.bat
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)
echo ✅ Gradle Wrapper检查通过

REM 检查Android SDK
echo.
echo [3/6] 检查Android SDK...
if "%ANDROID_HOME%"=="" (
    echo ⚠️  警告: 未设置ANDROID_HOME环境变量
    echo 尝试查找常见的Android SDK位置...
    
    set "SDK_PATHS=%LOCALAPPDATA%\Android\Sdk;C:\Android\Sdk;%USERPROFILE%\AppData\Local\Android\Sdk"
    set "FOUND_SDK="
    
    for %%p in (%SDK_PATHS%) do (
        if exist "%%p\platform-tools\adb.exe" (
            set "ANDROID_HOME=%%p"
            set "FOUND_SDK=1"
            echo ✅ 找到Android SDK: %%p
            goto :sdk_found
        )
    )
    
    if "%FOUND_SDK%"=="" (
        echo ❌ 错误: 未找到Android SDK
        echo 请安装Android Studio或Android SDK
        echo 或设置ANDROID_HOME环境变量
        pause
        exit /b 1
    )
    
    :sdk_found
) else (
    echo ✅ Android SDK: %ANDROID_HOME%
)

REM 设置环境变量
echo.
echo [4/6] 设置环境变量...
set "PATH=%ANDROID_HOME%\platform-tools;%ANDROID_HOME%\tools;%PATH%"
echo ✅ 环境变量设置完成

REM 清理项目
echo.
echo [5/6] 清理项目...
call gradlew.bat clean --no-daemon
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 项目清理失败
    echo 尝试删除.gradle目录...
    if exist ".gradle" rmdir /s /q ".gradle"
    echo 请重新运行脚本
    pause
    exit /b 1
)
echo ✅ 项目清理完成

REM 构建APK
echo.
echo [6/6] 构建APK...
echo 这可能需要几分钟时间，请耐心等待...
call gradlew.bat assembleDebug --no-daemon
if %ERRORLEVEL% NEQ 0 (
    echo ❌ APK构建失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查网络连接（需要下载依赖）
    echo 2. 确保Android SDK版本兼容
    echo 3. 使用Android Studio构建
    pause
    exit /b 1
)

REM 检查APK文件
echo.
echo ========================================
echo 构建完成！
echo ========================================

set "APK_PATH=app\build\outputs\apk\debug\app-debug.apk"
if exist "%APK_PATH%" (
    echo ✅ APK文件已生成: %APK_PATH%
    echo.
    echo 文件大小:
    for %%A in ("%APK_PATH%") do echo    %%~zA 字节
    echo.
    echo 下一步:
    echo 1. 将APK文件传输到Android设备
    echo 2. 在设备上启用"未知来源"安装
    echo 3. 安装并运行应用
    echo.
    echo 是否打开APK文件所在目录？ (Y/N)
    set /p "OPEN_DIR="
    if /i "%OPEN_DIR%"=="Y" (
        explorer "app\build\outputs\apk\debug"
    )
) else (
    echo ❌ 错误: 未找到APK文件
    echo 构建可能未完全成功
)

echo.
echo 构建脚本执行完成
pause
